import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// Types
export interface CartItem {
  id: string;
  name: string;
  price: string;
  image: string;
  category: string;
  quantity: number;
  selectedColor?: string;
  selectedSize?: string;
}

export interface WishlistItem {
  id: string;
  name: string;
  price: string;
  image: string;
  category: string;
}

interface CartWishlistState {
  cart: CartItem[];
  wishlist: WishlistItem[];
  cartPopup: {
    isVisible: boolean;
    item: CartItem | null;
  };
  wishlistPopup: {
    isVisible: boolean;
    item: WishlistItem | null;
  };
}

// Actions
type CartWishlistAction =
  | { type: 'ADD_TO_CART'; payload: Omit<CartItem, 'quantity'> & { quantity?: number } }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'UPDATE_CART_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'ADD_TO_WISHLIST'; payload: WishlistItem }
  | { type: 'REMOVE_FROM_WISHLIST'; payload: string }
  | { type: 'CLEAR_WISHLIST' }
  | { type: 'SHOW_CART_POPUP'; payload: CartItem }
  | { type: 'HIDE_CART_POPUP' }
  | { type: 'SHOW_WISHLIST_POPUP'; payload: WishlistItem }
  | { type: 'HIDE_WISHLIST_POPUP' }
  | { type: 'LOAD_FROM_STORAGE'; payload: Pick<CartWishlistState, 'cart' | 'wishlist'> };

// Context
interface CartWishlistContextType {
  state: CartWishlistState;
  addToCart: (item: Omit<CartItem, 'quantity'> & { quantity?: number }) => void;
  removeFromCart: (id: string) => void;
  updateCartQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
  addToWishlist: (item: WishlistItem) => void;
  removeFromWishlist: (id: string) => void;
  clearWishlist: () => void;
  isInCart: (id: string) => boolean;
  isInWishlist: (id: string) => boolean;
  getCartTotal: () => number;
  getCartItemCount: () => number;
  getWishlistItemCount: () => number;
  showCartPopup: (item: CartItem) => void;
  hideCartPopup: () => void;
  showWishlistPopup: (item: WishlistItem) => void;
  hideWishlistPopup: () => void;
}

const CartWishlistContext = createContext<CartWishlistContextType | undefined>(undefined);

// Reducer
const cartWishlistReducer = (state: CartWishlistState, action: CartWishlistAction): CartWishlistState => {
  switch (action.type) {
    case 'ADD_TO_CART': {
      const existingItem = state.cart.find(item => 
        item.id === action.payload.id && 
        item.selectedColor === action.payload.selectedColor && 
        item.selectedSize === action.payload.selectedSize
      );

      if (existingItem) {
        return {
          ...state,
          cart: state.cart.map(item =>
            item.id === existingItem.id && 
            item.selectedColor === existingItem.selectedColor && 
            item.selectedSize === existingItem.selectedSize
              ? { ...item, quantity: item.quantity + (action.payload.quantity || 1) }
              : item
          )
        };
      }

      return {
        ...state,
        cart: [...state.cart, { ...action.payload, quantity: action.payload.quantity || 1 }]
      };
    }

    case 'REMOVE_FROM_CART':
      return {
        ...state,
        cart: state.cart.filter(item => item.id !== action.payload)
      };

    case 'UPDATE_CART_QUANTITY':
      return {
        ...state,
        cart: state.cart.map(item =>
          item.id === action.payload.id
            ? { ...item, quantity: Math.max(0, action.payload.quantity) }
            : item
        ).filter(item => item.quantity > 0)
      };

    case 'CLEAR_CART':
      return {
        ...state,
        cart: []
      };

    case 'ADD_TO_WISHLIST': {
      const existingItem = state.wishlist.find(item => item.id === action.payload.id);
      if (existingItem) return state;

      return {
        ...state,
        wishlist: [...state.wishlist, action.payload]
      };
    }

    case 'REMOVE_FROM_WISHLIST':
      return {
        ...state,
        wishlist: state.wishlist.filter(item => item.id !== action.payload)
      };

    case 'CLEAR_WISHLIST':
      return {
        ...state,
        wishlist: []
      };

    case 'SHOW_CART_POPUP':
      return {
        ...state,
        cartPopup: {
          isVisible: true,
          item: action.payload
        }
      };

    case 'HIDE_CART_POPUP':
      return {
        ...state,
        cartPopup: {
          isVisible: false,
          item: null
        }
      };

    case 'SHOW_WISHLIST_POPUP':
      return {
        ...state,
        wishlistPopup: {
          isVisible: true,
          item: action.payload
        }
      };

    case 'HIDE_WISHLIST_POPUP':
      return {
        ...state,
        wishlistPopup: {
          isVisible: false,
          item: null
        }
      };

    case 'LOAD_FROM_STORAGE':
      return {
        ...state,
        ...action.payload,
        cartPopup: { isVisible: false, item: null },
        wishlistPopup: { isVisible: false, item: null }
      };

    default:
      return state;
  }
};

// Initial state
const initialState: CartWishlistState = {
  cart: [],
  wishlist: [],
  cartPopup: { isVisible: false, item: null },
  wishlistPopup: { isVisible: false, item: null }
};

// Provider component
export const CartWishlistProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(cartWishlistReducer, initialState);

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem('loriya_cart');
      const savedWishlist = localStorage.getItem('loriya_wishlist');
      
      if (savedCart || savedWishlist) {
        dispatch({
          type: 'LOAD_FROM_STORAGE',
          payload: {
            cart: savedCart ? JSON.parse(savedCart) : [],
            wishlist: savedWishlist ? JSON.parse(savedWishlist) : []
          }
        });
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error);
    }
  }, []);

  // Save to localStorage whenever state changes
  useEffect(() => {
    try {
      localStorage.setItem('loriya_cart', JSON.stringify(state.cart));
      localStorage.setItem('loriya_wishlist', JSON.stringify(state.wishlist));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }, [state.cart, state.wishlist]);

  // Action creators
  const addToCart = (item: Omit<CartItem, 'quantity'> & { quantity?: number }) => {
    dispatch({ type: 'ADD_TO_CART', payload: item });
  };

  const removeFromCart = (id: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: id });
  };

  const updateCartQuantity = (id: string, quantity: number) => {
    dispatch({ type: 'UPDATE_CART_QUANTITY', payload: { id, quantity } });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const addToWishlist = (item: WishlistItem) => {
    dispatch({ type: 'ADD_TO_WISHLIST', payload: item });
  };

  const removeFromWishlist = (id: string) => {
    dispatch({ type: 'REMOVE_FROM_WISHLIST', payload: id });
  };

  const clearWishlist = () => {
    dispatch({ type: 'CLEAR_WISHLIST' });
  };

  // Helper functions
  const isInCart = (id: string) => {
    return state.cart.some(item => item.id === id);
  };

  const isInWishlist = (id: string) => {
    return state.wishlist.some(item => item.id === id);
  };

  const getCartTotal = () => {
    return state.cart.reduce((total, item) => {
      const price = parseFloat(item.price.replace(/[^0-9.]/g, ''));
      return total + (price * item.quantity);
    }, 0);
  };

  const getCartItemCount = () => {
    return state.cart.reduce((total, item) => total + item.quantity, 0);
  };

  const getWishlistItemCount = () => {
    return state.wishlist.length;
  };

  // Popup action creators
  const showCartPopup = (item: CartItem) => {
    dispatch({ type: 'SHOW_CART_POPUP', payload: item });
  };

  const hideCartPopup = () => {
    dispatch({ type: 'HIDE_CART_POPUP' });
  };

  const showWishlistPopup = (item: WishlistItem) => {
    dispatch({ type: 'SHOW_WISHLIST_POPUP', payload: item });
  };

  const hideWishlistPopup = () => {
    dispatch({ type: 'HIDE_WISHLIST_POPUP' });
  };

  const value: CartWishlistContextType = {
    state,
    addToCart,
    removeFromCart,
    updateCartQuantity,
    clearCart,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInCart,
    isInWishlist,
    getCartTotal,
    getCartItemCount,
    getWishlistItemCount,
    showCartPopup,
    hideCartPopup,
    showWishlistPopup,
    hideWishlistPopup
  };

  return (
    <CartWishlistContext.Provider value={value}>
      {children}
    </CartWishlistContext.Provider>
  );
};

// Hook to use the context
export const useCartWishlist = () => {
  const context = useContext(CartWishlistContext);
  if (context === undefined) {
    throw new Error('useCartWishlist must be used within a CartWishlistProvider');
  }
  return context;
};
