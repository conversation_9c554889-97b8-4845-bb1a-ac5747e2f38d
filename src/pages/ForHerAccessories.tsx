import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { Filter, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { allProducts } from "../data/products";
import { Header } from '../components/Header';
import { MobileMenu } from '../components/MobileMenu';

const ForHerAccessories = () => {
  const [visibleCount, setVisibleCount] = useState(4);
  const [filterOpen, setFilterOpen] = useState(false);
  const [filterFade, setFilterFade] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Background image for consistency with main site
  const backgroundImage = "https://images.unsplash.com/photo-1553062407-98eeb64c6a62";

  const products = allProducts.accessories;

  const filterRef = useRef(null);
  const filterButtonRef = useRef(null);



  useEffect(() => {
    setVisibleCount(4);
  }, []);

  useEffect(() => {
    if (!filterOpen) return;
    function handleClickOutside(event) {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target) &&
        filterButtonRef.current &&
        !filterButtonRef.current.contains(event.target)
      ) {
        setFilterFade(true);
        setTimeout(() => {
          setFilterOpen(false);
          setFilterFade(false);
        }, 200);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [filterOpen]);

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />

      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />

      {/* Content Container */}
      <div className="relative z-20">
        {/* Header */}
        <Header onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)} />

        {/* Mobile Menu */}
        <MobileMenu
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
        />

        {/* Page Header Section */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="space-y-4">
                <Link
                  to="/gifts/for-her"
                  className="inline-flex items-center space-x-2 text-white/70 hover:text-white transition-colors text-sm font-light tracking-wide"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to For Her</span>
                </Link>
                <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                  Women's Collection
                </p>
                <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                  Accessories
                </h1>
                <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                  Explore our collection of luxury accessories, from elegant scarves to sophisticated small leather goods.
                  Each piece adds the perfect finishing touch to your style.
                </p>
              </div>
              <div className="flex justify-center pt-8 space-x-4 mb-8">
                <div className="relative">
                  <Button
                    ref={filterButtonRef}
                    variant="outline"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm flex items-center space-x-2"
                    onClick={() => {
                      if (filterOpen) {
                        setFilterFade(true);
                        setTimeout(() => {
                          setFilterOpen(false);
                          setFilterFade(false);
                        }, 200);
                      } else {
                        setFilterOpen(true);
                      }
                    }}
                  >
                    <Filter className="h-4 w-4" />
                    <span>Filters</span>
                  </Button>
                  {filterOpen && (
                    <div
                      ref={filterRef}
                      className={`absolute left-0 top-full mt-2 w-64 rounded-xl shadow-lg z-50 bg-black/40 backdrop-blur-lg border border-white/30 p-4 transition-opacity duration-200 ${filterFade ? 'opacity-0' : 'opacity-100'}`}
                      style={{
                        boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
                        background: "rgba(20,20,30,0.40)",
                        border: "1px solid rgba(255,255,255,0.22)",
                      }}
                    >
                      <h2 className="text-lg font-semibold text-white mb-6">Filter Products</h2>
                      <div className="space-y-4">
                        {/* Example: Price Range and Sort */}
                        <div className="text-white space-y-1">
                          <div className="mb-1">Price Range:</div>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="all" className="accent-white" />
                            <span>All</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="0-500" className="accent-white" />
                            <span>$0–$500</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="500-1000" className="accent-white" />
                            <span>$500–$1,000</span>
                          </label>
                          <label className="flex items-center space-x-2">
                            <input type="radio" name="priceRange" value="1000+" className="accent-white" />
                            <span>$1,000+</span>
                          </label>
                        </div>
                        <div className="text-white">
                          <label className="block mb-1">Sort by:</label>
                          <select className="w-full px-2 py-1 rounded bg-white/20 border border-white/30 text-white">
                            <option value="newest">Newest to Oldest</option>
                            <option value="oldest">Oldest to Newest</option>
                            <option value="priceLow">Price: Low to High</option>
                            <option value="priceHigh">Price: High to Low</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

              </div>
            </div>
          </div>
        </section>

        {/* Product Grid Section */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {products.slice(0, visibleCount).map((product) => (
                <div key={product.id} className="group cursor-pointer" onClick={() => window.location.href = `/gifts/for-her/accessories/${product.id}` }>
                  {/* Product Image */}
                  <div className="aspect-square bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden mb-4 relative border border-white/20">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                  </div>

                  {/* Product Info */}
                  <div className="text-center">
                    <h3 className="font-light text-sm mb-1 text-white/90 group-hover:text-white transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-sm text-white/70 font-light">{product.price}</p>
                  </div>
                </div>
              ))}
            </div>

            {visibleCount < products.length && (
              <div className="text-center mt-16">
                <Button
                  variant="outline"
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm px-8 py-3 font-light tracking-wide"
                  onClick={() => setVisibleCount(products.length)}
                >
                  Load More Products
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Footer */}
        <footer className="px-8 py-12">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-8">
              <div className="font-serif text-2xl font-light tracking-wider text-white">
                LORIYA
              </div>
              <div className="flex justify-center space-x-8 text-sm font-light text-white/70">
                <Link to="/" className="hover:text-white transition-colors">Home</Link>
                <a href="#" className="hover:text-white transition-colors">Collections</a>
                <a href="#" className="hover:text-white transition-colors">About</a>
                <a href="#" className="hover:text-white transition-colors">Contact</a>
              </div>
              <div className="pt-8 border-t border-white/20">
                <p className="text-xs font-light text-white/50">
                  © 2024 Loriya. All rights reserved.
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default ForHerAccessories; 