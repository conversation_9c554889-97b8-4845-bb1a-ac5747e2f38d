import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Shirt, Pause } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import SharedNav from "@/components/SharedNav";

const Index = () => {
  const [email, setEmail] = useState("");
  const [contactName, setContactName] = useState("");
  const [contactEmail, setContactEmail] = useState("");
  const [contactMessage, setContactMessage] = useState("");



  const loriyaServices = [
    {
      title: "Book An Appointment",
      description: "Enjoy priority access to the boutique of your choice. When you arrive, your Client Advisor will guide you through a hand-picked selection.",
      imageUrl: "https://images.unsplash.com/photo-1573496004836-31a3d34a44b1?q=80&w=2369&auto=format&fit=crop",
      alt: "A client advisor assisting a customer in a luxury boutique."
    },
    {
      title: "Complimentary Shipping & Returns",
      description: "Enjoy shipping in 1-2 business days, complimentary returns and streamlined exchanges with no extra cost.",
      imageUrl: "https://images.unsplash.com/photo-1617128038662-de2954a4857d?q=80&w=2370&auto=format&fit=crop",
      alt: "Luxury branded shopping bags and boxes."
    },
    {
      title: "Personalisation",
      description: "Emboss select bags, luggage, and leather accessories with your initials to create a truly unique piece.",
      imageUrl: "https://images.unsplash.com/photo-1506929562927-5a5f67972b2a?q=80&w=2370&auto=format&fit=crop",
      alt: "Tools for personalising leather goods."
    }
  ];

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Newsletter signup:", email);
    setEmail("");
    toast.success("Thank you for subscribing!");
  };

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Contact form submitted:", { name: contactName, email: contactEmail, message: contactMessage });
    toast.success("Your message has been sent!");
    setContactName("");
    setContactEmail("");
    setContactMessage("");
  };

  const backgroundImage = "https://images.unsplash.com/photo-1506152983158-b4a74a01c721";

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div 
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/60 z-10" />
      
      {/* Content Container */}
      <div className="relative z-20">
        <SharedNav />

        {/* Hero Section */}
        <section className="px-8 py-16 md:py-24">
          <div className="max-w-6xl mx-auto">
            <div className="text-center">
              <div className="space-y-8">
                <div className="space-y-4">
                  <p className="text-sm font-light tracking-widest text-white/80 uppercase">
                    Spring Collection 2024
                  </p>
                  <h1 className="font-serif text-4xl md:text-6xl font-light leading-tight text-white">
                    Timeless
                    <br />
                    Elegance
                  </h1>
                  <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                    Discover our carefully curated collection of contemporary essentials, 
                    crafted with attention to detail and sustainable practices.
                  </p>
                </div>
                <div className="flex justify-center pt-4">
                  <a href="#" className="text-lg font-light tracking-wider text-white uppercase inline-block hover:text-gray-200 transition-colors">
                    Shop Now
                    <span className="block w-full h-px bg-white mt-1"></span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Collections */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <p className="text-sm font-light tracking-widest text-white/70 uppercase">
                Featured Collections
              </p>
              <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                Curated for You
              </h2>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { title: "Women", subtitle: "Handbags", imageUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=2187&auto=format&fit=crop" },
                { title: "Men", subtitle: "Bags", imageUrl: "https://images.unsplash.com/photo-1547949003-9792a18a2601?q=80&w=2370&auto=format&fit=crop" },
                { title: "Kids", subtitle: "Playwear", imageUrl: "https://images.unsplash.com/photo-1519340241574-289a4d415523?q=80&w=2264&auto=format&fit=crop" },
                { title: "Accessories", subtitle: "Essentials", imageUrl: "https://images.unsplash.com/photo-1526328828355-69b068d1c72a?q=80&w=2370&auto=format&fit=crop" }
              ].map((collection) => (
                <div key={collection.title} className="group relative aspect-[3/4] cursor-pointer overflow-hidden rounded-lg">
                  <img src={collection.imageUrl} alt={collection.title} className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute inset-0 flex flex-col justify-between p-4 text-white sm:p-6">
                      <div className="text-right">
                          <span className="rounded-sm bg-black/50 px-3 py-1.5 text-[10px] font-light uppercase tracking-widest text-white backdrop-blur-sm">
                              {collection.title}
                          </span>
                      </div>
                      <div className="text-center">
                          <h3 className="font-serif text-2xl font-light mb-2">{collection.subtitle}</h3>
                          <a href="#" className="inline-block border border-white px-4 py-2 text-xs font-light uppercase tracking-widest transition-colors duration-300 hover:bg-white hover:text-black">
                              Shop The Collection
                          </a>
                      </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="px-8 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <p className="text-sm font-light tracking-widest text-white/70 uppercase">
                  Our Philosophy
                </p>
                <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                  Crafted with Purpose
                </h2>
              </div>
              <p className="text-lg font-light text-white/90 leading-relaxed max-w-2xl mx-auto">
                We believe in creating garments that transcend seasons and trends. 
                Each piece is thoughtfully designed and ethically made, using only 
                the finest sustainable materials.
              </p>
              <div className="flex justify-center space-x-12 pt-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                    <Shirt className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-sm font-light text-white/80">Sustainable Materials</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                    <ShoppingBag className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-sm font-light text-white/80">Ethical Production</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Loriya Services Section */}
        <section className="px-8 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                Loriya Services
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-12 text-white">
              {loriyaServices.map((service) => (
                <div key={service.title}>
                  <div className="relative mb-6 group">
                    <div className="aspect-square bg-white/10 rounded-lg overflow-hidden border border-white/20">
                      <img src={service.imageUrl} alt={service.alt} className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" />
                    </div>
                    <button className="absolute top-4 right-4 bg-black/30 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center cursor-pointer hover:bg-black/50 transition-colors">
                      <Pause className="w-5 h-5 text-white" />
                    </button>
                  </div>
                  <div className="text-center px-2">
                    <h3 className="text-sm font-light tracking-[0.2em] uppercase mb-4">{service.title}</h3>
                    <p className="text-sm font-light text-white/80 leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="px-8 py-16">
          <div className="max-w-2xl mx-auto text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="font-serif text-3xl md:text-4xl font-light text-white">
                  Stay Connected
                </h2>
                <p className="text-lg font-light text-white/80">
                  Be the first to know about new collections and exclusive offers.
                </p>
              </div>
              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/60 backdrop-blur-sm flex-1"
                  required
                />
                <Button 
                  type="submit"
                  className="bg-white text-black hover:bg-gray-100 px-8 py-2 font-light tracking-wide"
                >
                  Subscribe
                </Button>
              </form>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="px-8 py-12">
          <div className="max-w-6xl mx-auto">
            <div className="text-center space-y-8">
              <div className="font-serif text-2xl font-light tracking-wider text-white">
                LORIYA
              </div>
              <div className="flex justify-center space-x-8 text-sm font-light text-white/70">
                <a href="#" className="hover:text-white transition-colors">Collections</a>
                <a href="#" className="hover:text-white transition-colors">About</a>
                <a href="#" className="hover:text-white transition-colors">Contact</a>
                <a href="#" className="hover:text-white transition-colors">Size Guide</a>
              </div>
              <div className="pt-8 border-t border-white/20">
                <p className="text-xs font-light text-white/50">
                  © 2024 Loriya. All rights reserved.
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
