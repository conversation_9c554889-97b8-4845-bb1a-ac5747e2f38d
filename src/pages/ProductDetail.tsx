import { use<PERSON>ara<PERSON>, Link, useNavigate } from "react-router-dom";
import SharedNav from "@/components/SharedNav";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Heart, X, ShoppingCart } from "lucide-react";
import { useState } from "react";
import { allProducts } from "../data/products";

const categoryNames = {
  bags: "Bags",
  shoes: "Shoes",
  jewelry: "Jewelry",
  accessories: "Accessories",
  follower: "Flowers & Perfumes",
};

const colorOptions = [
  { name: "Black", value: "black", hex: "#222" },
  { name: "<PERSON>", value: "brown", hex: "#a0522d" },
  { name: "White", value: "white", hex: "#fff" },
  { name: "Red", value: "red", hex: "#c0392b" },
];
const sizeOptions = ["XS", "S", "M", "L", "XL", "EU 36", "EU 37", "EU 38", "EU 39", "EU 40"];

const ProductDetail = () => {
  const { category, productId } = useParams();
  const navigate = useNavigate();
  const products = allProducts[category] || [];
  const product = products.find((p) => String(p.id) === String(productId));

  const [selectedColor, setSelectedColor] = useState(colorOptions[0].value);
  const [selectedSize, setSelectedSize] = useState(sizeOptions[0]);
  const [wishlisted, setWishlisted] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedLook, setSelectedLook] = useState(null);

  // Product images array (for now using the same image, but can be different angles)
  const productImages = [
    product?.image || '',
    product?.image || '',
    product?.image || '',
    product?.image || ''
  ];

  // Product availability (you can make this dynamic based on product data)
  const isAvailable = product?.available !== false; // Default to available unless explicitly set to false

  // Look data for the popup
  const lookData = [
    {
      id: 1,
      title: "Casual Chic",
      subtitle: "Perfect for everyday elegance",
      image: product?.image || '',
      description: "A sophisticated casual look that combines comfort with style. Perfect for brunch dates, shopping trips, or casual office days.",
      items: [
        { name: product?.name || "Featured Item", price: product?.price || "$199.00" },
        { name: "Classic White Blouse", price: "$89.00" },
        { name: "Tailored Trousers", price: "$129.00" },
        { name: "Minimalist Watch", price: "$199.00" }
      ]
    },
    {
      id: 2,
      title: "Office Ready",
      subtitle: "Professional sophistication",
      image: product?.image || '',
      description: "A polished professional ensemble that commands respect in any boardroom. Sophisticated, confident, and effortlessly chic.",
      items: [
        { name: product?.name || "Featured Item", price: product?.price || "$199.00" },
        { name: "Silk Blazer", price: "$249.00" },
        { name: "Pencil Skirt", price: "$119.00" },
        { name: "Pearl Earrings", price: "$79.00" }
      ]
    },
    {
      id: 3,
      title: "Evening Glam",
      subtitle: "Night out perfection",
      image: product?.image || '',
      description: "Turn heads with this glamorous evening look. Perfect for dinner dates, cocktail parties, or special celebrations.",
      items: [
        { name: product?.name || "Featured Item", price: product?.price || "$199.00" },
        { name: "Little Black Dress", price: "$189.00" },
        { name: "Statement Heels", price: "$159.00" },
        { name: "Diamond Necklace", price: "$299.00" }
      ]
    },
    {
      id: 4,
      title: "Weekend Vibes",
      subtitle: "Relaxed luxury styling",
      image: product?.image || '',
      description: "Effortless weekend style that doesn't compromise on elegance. Perfect for casual outings and relaxed social gatherings.",
      items: [
        { name: product?.name || "Featured Item", price: product?.price || "$199.00" },
        { name: "Cashmere Sweater", price: "$169.00" },
        { name: "Designer Jeans", price: "$149.00" },
        { name: "Leather Sneakers", price: "$199.00" }
      ]
    }
  ];

  if (!product) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-black/80">
        <SharedNav />
        <div className="text-white text-2xl">Product not found.</div>
        <Button className="mt-6" onClick={() => navigate(-1)}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div className="fixed inset-0 bg-cover bg-center bg-no-repeat" style={{ backgroundImage: `url('${product.image}')` }} />
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/70 z-10" />
      <div className="relative z-20">
        <SharedNav />
        <div className="flex items-center justify-center min-h-[70vh] px-4 py-8">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-6 lg:gap-8 max-w-6xl w-full">
            {/* Product Details Box - Left Side on Desktop, Top on Mobile */}
            <div className="w-full lg:max-w-lg bg-black/40 backdrop-blur-lg rounded-2xl border border-white/20 shadow-2xl p-4 lg:p-5 flex flex-col items-center">
            <Link to={`/gifts/for-her/${category}`} className="self-start mb-3 inline-flex items-center space-x-2 text-white/70 hover:text-white transition-colors text-sm font-light tracking-wide">
              <ArrowLeft className="h-4 w-4" />
              <span>Back to {categoryNames[category] || "Category"}</span>
            </Link>
            <img src={productImages[selectedImageIndex]} alt={product.name} className="w-32 h-32 sm:w-40 sm:h-40 object-cover rounded-xl border border-white/30 mb-3 shadow-lg" />
            <h1 className="text-lg sm:text-xl font-serif text-white mb-1 text-center">{product.name}</h1>
            <div className="text-base sm:text-lg text-white/80 mb-2">{product.price}</div>

            {/* Availability Status */}
            <div className="flex items-center justify-center mb-2">
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${
                isAvailable
                  ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                  : 'bg-red-500/20 text-red-300 border border-red-500/30'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  isAvailable ? 'bg-green-400' : 'bg-red-400'
                }`} />
                <span>{isAvailable ? 'In Stock' : 'Out of Stock'}</span>
              </div>
            </div>

            <div className="text-white/80 text-center mb-3 sm:mb-4 text-xs sm:text-sm max-w-xs px-2">{product.description || "Spacious and iconic, perfect for daily use."}</div>
            {/* Color selection */}
            <div className="mb-2 w-full">
              <div className="text-white/80 mb-1.5 text-xs sm:text-sm">Color:</div>
              <div className="flex space-x-2 justify-center">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    className={`w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${selectedColor === color.value ? 'border-white scale-110' : 'border-white/30'}`}
                    style={{ background: color.hex }}
                    onClick={() => setSelectedColor(color.value)}
                    aria-label={color.name}
                  >
                    {selectedColor === color.value && <span className="block w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full border border-white bg-white/40" />}
                  </button>
                ))}
              </div>
            </div>
            {/* Size selection */}
            <div className="mb-3 w-full">
              <div className="text-white/80 mb-1.5 text-xs sm:text-sm">Size:</div>
              <div className="flex flex-wrap gap-1 justify-center">
                {sizeOptions.map((size) => (
                  <button
                    key={size}
                    className={`px-1.5 sm:px-2 py-0.5 rounded-lg border text-xs font-light transition-all duration-200 backdrop-blur-sm ${selectedSize === size ? 'bg-white/20 border-white text-white' : 'bg-black/30 border-white/30 text-white/70 hover:bg-white/10'}`}
                    onClick={() => setSelectedSize(size)}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>
            {/* Wishlist and Add to Cart */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full">
              <Button
                variant="outline"
                className={`bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm px-3 sm:px-4 py-2 font-light tracking-wide flex items-center justify-center space-x-1.5 text-xs sm:text-sm flex-1 ${wishlisted ? 'border-red-400 text-red-300' : ''}`}
                onClick={() => setWishlisted((w) => !w)}
              >
                <Heart className={`h-3 w-3 sm:h-3.5 sm:w-3.5 ${wishlisted ? 'fill-red-400 text-red-400' : 'text-white'}`} />
                <span className="hidden sm:inline">{wishlisted ? 'Wishlisted' : 'Add to Wishlist'}</span>
                <span className="sm:hidden">{wishlisted ? 'Wishlisted' : 'Wishlist'}</span>
              </Button>
              <Button
                variant="outline"
                className={`backdrop-blur-sm px-3 sm:px-4 py-2 font-light tracking-wide text-xs sm:text-sm flex-1 ${
                  isAvailable
                    ? 'bg-white/10 border-white/30 text-white hover:bg-white/20'
                    : 'bg-gray-500/20 border-gray-500/30 text-gray-400 cursor-not-allowed'
                }`}
                disabled={!isAvailable}
              >
                {isAvailable ? 'Add to Cart' : 'Out of Stock'}
              </Button>
            </div>
            </div>

            {/* Product Angle Images and Info - Right Side on Desktop, Bottom on Mobile */}
            <div className="w-full flex flex-col lg:flex-row gap-4 lg:gap-6">
              {/* Angle Images */}
              <div className="flex flex-row lg:flex-col gap-2 lg:gap-3 justify-center lg:justify-start">
                {productImages.map((image, index) => (
                  <div
                    key={index}
                    className={`w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-black/40 backdrop-blur-lg rounded-lg lg:rounded-xl border shadow-lg overflow-hidden hover:scale-105 transition-all duration-200 cursor-pointer ${
                      selectedImageIndex === index
                        ? 'border-white/60 ring-2 ring-white/40 scale-105'
                        : 'border-white/20'
                    }`}
                    onClick={() => setSelectedImageIndex(index)}
                  >
                    <img
                      src={image}
                      alt={`${product.name} - Angle ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>

              {/* Product Information Text */}
              <div className="w-full lg:max-w-xs bg-black/30 backdrop-blur-lg rounded-xl border border-white/20 p-3 lg:p-4 shadow-lg">
                <h3 className="text-white font-serif text-base lg:text-lg mb-2 lg:mb-3">Product Details</h3>
                <div className="space-y-2 lg:space-y-3 text-xs lg:text-sm text-white/80">
                  <p>
                    This exquisite {product.name.toLowerCase()} combines luxury craftsmanship with modern elegance.
                    Made from premium materials, it's designed to complement your sophisticated style.
                  </p>
                  <p className="hidden sm:block">
                    Perfect for both casual and formal occasions, this piece features meticulous attention
                    to detail and superior quality that ensures lasting beauty.
                  </p>
                  <div className="border-t border-white/20 pt-2 lg:pt-3 mt-3 lg:mt-4">
                    <h4 className="text-white font-medium mb-1 lg:mb-2 text-sm lg:text-base">Delivery Information</h4>
                    <div className="space-y-1">
                      <p className="flex justify-between text-xs lg:text-sm">
                        <span>Standard Delivery:</span>
                        <span className="text-white">5-7 days</span>
                      </p>
                      <p className="flex justify-between text-xs lg:text-sm">
                        <span>Express Delivery:</span>
                        <span className="text-white">2-3 days</span>
                      </p>
                      <p className="flex justify-between text-xs lg:text-sm">
                        <span>Premium Delivery:</span>
                        <span className="text-white">Next day</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Get the Look Section */}
        <div className="px-4 py-8 lg:py-12">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl lg:text-3xl font-serif text-white text-center mb-6 lg:mb-8">Get the Look</h2>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-6">
              {lookData.map((look, index) => (
                <div
                  key={look.id}
                  className="bg-black/40 backdrop-blur-lg rounded-xl lg:rounded-2xl border border-white/20 shadow-lg overflow-hidden hover:scale-105 transition-transform duration-300 cursor-pointer"
                  onClick={() => setSelectedLook(look)}
                >
                  <div className={`${index < 2 ? 'aspect-[2/3]' : 'aspect-square'} lg:aspect-[2/3] relative`}>
                    <img
                      src={look.image}
                      alt={`${look.title} - Complete Look`}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    <div className="absolute bottom-2 lg:bottom-3 left-2 lg:left-3 right-2 lg:right-3">
                      <h3 className="text-white font-medium text-xs lg:text-sm mb-1">{look.title}</h3>
                      <p className="text-white/80 text-xs">{look.subtitle}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Look Popup Modal */}
        {selectedLook && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-black/60 backdrop-blur-lg rounded-2xl border border-white/20 shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              {/* Header */}
              <div className="flex items-center justify-between p-4 lg:p-6 border-b border-white/20">
                <div>
                  <h2 className="text-xl lg:text-2xl font-serif text-white">{selectedLook.title}</h2>
                  <p className="text-white/80 text-sm">{selectedLook.subtitle}</p>
                </div>
                <button
                  onClick={() => setSelectedLook(null)}
                  className="text-white/70 hover:text-white transition-colors p-2"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {/* Content */}
              <div className="p-4 lg:p-6">
                {/* Look Image */}
                <div className="aspect-[3/4] lg:aspect-[2/3] mb-4 lg:mb-6 rounded-xl overflow-hidden">
                  <img
                    src={selectedLook.image}
                    alt={selectedLook.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Description */}
                <p className="text-white/80 text-sm lg:text-base mb-4 lg:mb-6 leading-relaxed">
                  {selectedLook.description}
                </p>

                {/* Items in this look */}
                <div className="space-y-3">
                  <h3 className="text-white font-medium text-lg mb-3">Items in this look:</h3>
                  {selectedLook.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg border border-white/10">
                      <div>
                        <h4 className="text-white font-medium text-sm">{item.name}</h4>
                        <p className="text-white/60 text-xs">Premium Quality</p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-white font-medium">{item.price}</span>
                        <Button
                          size="sm"
                          className="bg-white/10 hover:bg-white/20 text-white border-white/30 text-xs px-3 py-1"
                        >
                          <ShoppingCart className="h-3 w-3 mr-1" />
                          Add
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Complete Look Button */}
                <div className="mt-6 pt-4 border-t border-white/20">
                  <Button className="w-full bg-white/10 hover:bg-white/20 text-white border border-white/30 py-3">
                    Shop Complete Look - {selectedLook.items.reduce((total, item) => {
                      const price = parseFloat(item.price.replace('$', '').replace(',', ''));
                      return total + price;
                    }, 0).toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetail; 