import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router-dom";
import SharedNav from "@/components/SharedNav";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Heart } from "lucide-react";
import { useState } from "react";
import { allProducts } from "../data/products";

const categoryNames = {
  bags: "Bags",
  shoes: "Shoes",
  jewelry: "Jewelry",
  accessories: "Accessories",
  follower: "Flowers & Perfumes",
};

const colorOptions = [
  { name: "Black", value: "black", hex: "#222" },
  { name: "<PERSON>", value: "brown", hex: "#a0522d" },
  { name: "White", value: "white", hex: "#fff" },
  { name: "Red", value: "red", hex: "#c0392b" },
];
const sizeOptions = ["XS", "S", "M", "L", "XL", "EU 36", "EU 37", "EU 38", "EU 39", "EU 40"];

const ProductDetail = () => {
  const { category, productId } = useParams();
  const navigate = useNavigate();
  const products = allProducts[category] || [];
  const product = products.find((p) => String(p.id) === String(productId));

  const [selectedColor, setSelectedColor] = useState(colorOptions[0].value);
  const [selectedSize, setSelectedSize] = useState(sizeOptions[0]);
  const [wishlisted, setWishlisted] = useState(false);

  if (!product) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-black/80">
        <SharedNav />
        <div className="text-white text-2xl">Product not found.</div>
        <Button className="mt-6" onClick={() => navigate(-1)}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative">
      {/* Full Background Image */}
      <div className="fixed inset-0 bg-cover bg-center bg-no-repeat" style={{ backgroundImage: `url('${product.image}')` }} />
      {/* Overlay for better text readability */}
      <div className="fixed inset-0 bg-black/70 z-10" />
      <div className="relative z-20">
        <SharedNav />
        <div className="flex items-center justify-center min-h-[70vh] px-4 py-8">
          <div className="flex items-center gap-8 max-w-6xl w-full">
            {/* Product Details Box - Left Side */}
            <div className="max-w-lg w-full bg-black/40 backdrop-blur-lg rounded-2xl border border-white/20 shadow-2xl p-5 flex flex-col items-center">
            <Link to={`/gifts/for-her/${category}`} className="self-start mb-3 inline-flex items-center space-x-2 text-white/70 hover:text-white transition-colors text-sm font-light tracking-wide">
              <ArrowLeft className="h-4 w-4" />
              <span>Back to {categoryNames[category] || "Category"}</span>
            </Link>
            <img src={product.image} alt={product.name} className="w-40 h-40 object-cover rounded-xl border border-white/30 mb-3 shadow-lg" />
            <h1 className="text-xl font-serif text-white mb-1 text-center">{product.name}</h1>
            <div className="text-lg text-white/80 mb-2">{product.price}</div>
            <div className="text-white/80 text-center mb-4 text-sm max-w-xs">{product.description || "Spacious and iconic, perfect for daily use."}</div>
            {/* Color selection */}
            <div className="mb-2 w-full">
              <div className="text-white/80 mb-1.5 text-sm">Color:</div>
              <div className="flex space-x-2 justify-center">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${selectedColor === color.value ? 'border-white scale-110' : 'border-white/30'}`}
                    style={{ background: color.hex }}
                    onClick={() => setSelectedColor(color.value)}
                    aria-label={color.name}
                  >
                    {selectedColor === color.value && <span className="block w-1.5 h-1.5 rounded-full border border-white bg-white/40" />}
                  </button>
                ))}
              </div>
            </div>
            {/* Size selection */}
            <div className="mb-3 w-full">
              <div className="text-white/80 mb-1.5 text-sm">Size:</div>
              <div className="flex flex-wrap gap-1 justify-center">
                {sizeOptions.map((size) => (
                  <button
                    key={size}
                    className={`px-2 py-0.5 rounded-lg border text-xs font-light transition-all duration-200 backdrop-blur-sm ${selectedSize === size ? 'bg-white/20 border-white text-white' : 'bg-black/30 border-white/30 text-white/70 hover:bg-white/10'}`}
                    onClick={() => setSelectedSize(size)}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>
            {/* Wishlist and Add to Cart */}
            <div className="flex space-x-2 w-full">
              <Button
                variant="outline"
                className={`bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm px-4 py-2 font-light tracking-wide flex items-center justify-center space-x-1.5 text-sm flex-1 ${wishlisted ? 'border-red-400 text-red-300' : ''}`}
                onClick={() => setWishlisted((w) => !w)}
              >
                <Heart className={`h-3.5 w-3.5 ${wishlisted ? 'fill-red-400 text-red-400' : 'text-white'}`} />
                <span>{wishlisted ? 'Wishlisted' : 'Add to Wishlist'}</span>
              </Button>
              <Button variant="outline" className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm px-4 py-2 font-light tracking-wide text-sm flex-1">Add to Cart</Button>
            </div>
            </div>

            {/* Product Angle Images - Right Side */}
            <div className="flex flex-col gap-3">
              <div className="w-24 h-24 bg-black/40 backdrop-blur-lg rounded-xl border border-white/20 shadow-lg overflow-hidden hover:scale-105 transition-transform duration-200 cursor-pointer">
                <img src={product.image} alt={`${product.name} - Angle 1`} className="w-full h-full object-cover" />
              </div>
              <div className="w-24 h-24 bg-black/40 backdrop-blur-lg rounded-xl border border-white/20 shadow-lg overflow-hidden hover:scale-105 transition-transform duration-200 cursor-pointer">
                <img src={product.image} alt={`${product.name} - Angle 2`} className="w-full h-full object-cover" />
              </div>
              <div className="w-24 h-24 bg-black/40 backdrop-blur-lg rounded-xl border border-white/20 shadow-lg overflow-hidden hover:scale-105 transition-transform duration-200 cursor-pointer">
                <img src={product.image} alt={`${product.name} - Angle 3`} className="w-full h-full object-cover" />
              </div>
              <div className="w-24 h-24 bg-black/40 backdrop-blur-lg rounded-xl border border-white/20 shadow-lg overflow-hidden hover:scale-105 transition-transform duration-200 cursor-pointer">
                <img src={product.image} alt={`${product.name} - Angle 4`} className="w-full h-full object-cover" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail; 