import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { CartWishlistProvider } from "./contexts/CartWishlistContext";
import Index from "./pages/Index";
import ForHer from "./pages/ForHer";
import ForHerBags from "./pages/ForHerBags";
import ForHerShoes from "./pages/ForHerShoes";
import ForHerJewelry from "./pages/ForHerJewelry";
import ForHerAccessories from "./pages/ForHerAccessories";
import ForHerFollower from "./pages/ForHerFollower";
import NotFound from "./pages/NotFound";
import ProductDetail from "./pages/ProductDetail";

const queryClient = new QueryClient();

// Component to handle scroll behavior with position memory
const ScrollManager = () => {
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;
    const isProductsPage = currentPath === '/gifts/for-her' || currentPath === '/gifts/for-her/bags' ||
                          currentPath === '/gifts/for-her/shoes' || currentPath === '/gifts/for-her/jewelry' ||
                          currentPath === '/gifts/for-her/accessories' || currentPath === '/gifts/for-her/follower' ||
                          currentPath === '/';

    // Handle scroll behavior based on page type
    if (isProductsPage) {
      // Check if returning from product detail page
      const savedPosition = sessionStorage.getItem(`scrollPosition_${currentPath}`);

      if (savedPosition) {
        // Returning from product detail - restore previous position
        const timer = setTimeout(() => {
          window.scrollTo({
            top: parseInt(savedPosition),
            behavior: 'smooth'
          });
        }, 300);
        return () => clearTimeout(timer);
      } else {
        // First visit or fresh navigation - scroll to middle
        window.scrollTo(0, 0);

        const timer = setTimeout(() => {
          const viewportHeight = window.innerHeight;
          const documentHeight = document.documentElement.scrollHeight;

          const targetPosition = Math.min(
            viewportHeight * 0.3,
            (documentHeight - viewportHeight) * 0.4
          );

          window.scrollTo({
            top: Math.max(0, targetPosition),
            behavior: 'smooth'
          });
        }, 500);
        return () => clearTimeout(timer);
      }
    } else {
      // For other pages, just scroll to top
      window.scrollTo(0, 0);
    }
  }, [location.pathname]);

  // Save scroll position when navigating away from products pages
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a');

      if (link) {
        const currentPath = location.pathname;
        const isProductsPage = currentPath === '/gifts/for-her' || currentPath === '/gifts/for-her/bags' ||
                              currentPath === '/gifts/for-her/shoes' || currentPath === '/gifts/for-her/jewelry' ||
                              currentPath === '/gifts/for-her/accessories' || currentPath === '/gifts/for-her/follower' ||
                              currentPath === '/';

        // Check if clicking on a product link (going to product detail)
        const href = link.getAttribute('href') || link.getAttribute('to') || '';
        const isGoingToProductDetail = href.includes('/gifts/for-her/') && href.split('/').length > 4;

        if (isProductsPage && isGoingToProductDetail) {
          sessionStorage.setItem(`scrollPosition_${currentPath}`, window.scrollY.toString());
        }
      }
    };

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [location.pathname]);

  return null;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <CartWishlistProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollManager />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/gifts/for-her" element={<ForHer />} />
            <Route path="/gifts/for-her/bags" element={<ForHerBags />} />
            <Route path="/gifts/for-her/shoes" element={<ForHerShoes />} />
            <Route path="/gifts/for-her/jewelry" element={<ForHerJewelry />} />
            <Route path="/gifts/for-her/accessories" element={<ForHerAccessories />} />
            <Route path="/gifts/for-her/follower" element={<ForHerFollower />} />
            <Route path="/gifts/for-her/:category/:productId" element={<ProductDetail />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </CartWishlistProvider>
  </QueryClientProvider>
);

export default App;
