import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { useEffect } from "react";
import Index from "./pages/Index";
import ForHer from "./pages/ForHer";
import ForHerBags from "./pages/ForHerBags";
import ForHerShoes from "./pages/ForHerShoes";
import ForHerJewelry from "./pages/ForHerJewelry";
import ForHerAccessories from "./pages/ForHerAccessories";
import ForHerFollower from "./pages/ForHerFollower";
import NotFound from "./pages/NotFound";
import ProductDetail from "./pages/ProductDetail";

const queryClient = new QueryClient();

// Component to handle scroll behavior on route changes
const ScrollToMiddle = () => {
  const location = useLocation();

  useEffect(() => {
    // First scroll to top immediately to reset position
    window.scrollTo(0, 0);

    // Then wait for content to load and scroll to middle
    const timer = setTimeout(() => {
      const viewportHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // Calculate middle position - aim for center of viewport
      const middlePosition = Math.max(0, (documentHeight - viewportHeight) / 3);

      // Smooth scroll to middle
      window.scrollTo({
        top: middlePosition,
        behavior: 'smooth'
      });
    }, 500); // Increased delay to ensure content is loaded

    return () => clearTimeout(timer);
  }, [location.pathname]);

  return null;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <ScrollToMiddle />
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/gifts/for-her" element={<ForHer />} />
          <Route path="/gifts/for-her/bags" element={<ForHerBags />} />
          <Route path="/gifts/for-her/shoes" element={<ForHerShoes />} />
          <Route path="/gifts/for-her/jewelry" element={<ForHerJewelry />} />
          <Route path="/gifts/for-her/accessories" element={<ForHerAccessories />} />
          <Route path="/gifts/for-her/follower" element={<ForHerFollower />} />
          <Route path="/gifts/for-her/:category/:productId" element={<ProductDetail />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
