import React, { useEffect } from 'react';
import { X, ShoppingBag, Eye, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface CartPopupProps {
  isVisible: boolean;
  item: {
    id: string;
    name: string;
    price: string;
    image: string;
    category: string;
    selectedColor?: string;
    selectedSize?: string;
    quantity: number;
  } | null;
  onClose: () => void;
  onViewCart: () => void;
}

const CartPopup: React.FC<CartPopupProps> = ({ isVisible, item, onClose, onViewCart }) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 4000); // Auto close after 4 seconds

      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible || !item) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />

      {/* Popup positioned at center */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-black/90 backdrop-blur-lg border border-white/20 rounded-xl shadow-2xl overflow-hidden animate-in slide-in-from-top-2 duration-300">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/20">
            <div className="flex items-center space-x-3">
              <ShoppingBag className="h-6 w-6 text-white" />
              <h2 className="text-xl font-serif text-white">
                Added to Cart!
              </h2>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Product Details */}
            <div className="flex space-x-4">
              {/* Product Image */}
              <div className="w-20 h-20 bg-white/10 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Product Info */}
              <div className="flex-1 min-w-0">
                <h3 className="text-white font-medium text-lg truncate">{item.name}</h3>
                <p className="text-white/60 text-sm capitalize mb-2">{item.category}</p>
                <p className="text-white font-medium text-lg">{item.price}</p>

                {/* Options */}
                <div className="mt-2 space-y-1">
                  {item.selectedColor && (
                    <p className="text-sm text-white/70">Color: {item.selectedColor}</p>
                  )}
                  {item.selectedSize && (
                    <p className="text-sm text-white/70">Size: {item.selectedSize}</p>
                  )}
                  <p className="text-sm text-white/70">Quantity: {item.quantity}</p>
                </div>
              </div>
            </div>

            {/* Success Message */}
            <div className="text-center py-4">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <ShoppingBag className="h-6 w-6 text-white" />
              </div>
              <p className="text-white text-lg font-medium">Successfully added to cart!</p>
              <p className="text-white/60 text-sm mt-1">Ready for checkout</p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                className="w-full bg-white text-black hover:bg-white/90 font-medium"
                onClick={() => {
                  onViewCart();
                  onClose();
                }}
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                View Shopping Cart
              </Button>

              <Link
                to={`/gifts/for-her/${item.category}/${item.id}`}
                className="block"
                onClick={onClose}
              >
                <Button
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Item Details
                </Button>
              </Link>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="h-1 bg-white/10">
            <div
              className="h-full bg-green-500"
              style={{
                animation: 'shrink 4s linear forwards'
              }}
            />
          </div>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </>
  );
};

export default CartPopup;
