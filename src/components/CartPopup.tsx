import React, { useEffect } from 'react';
import { X, ShoppingBag, Eye, ShoppingCart } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface CartPopupProps {
  isVisible: boolean;
  item: {
    id: string;
    name: string;
    price: string;
    image: string;
    category: string;
    selectedColor?: string;
    selectedSize?: string;
    quantity: number;
  } | null;
  onClose: () => void;
  onViewCart: () => void;
}

const CartPopup: React.FC<CartPopupProps> = ({ isVisible, item, onClose, onViewCart }) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 4000); // Auto close after 4 seconds

      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible || !item) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-50 pointer-events-none">
        {/* Popup positioned at top-right */}
        <div className="absolute top-4 right-4 w-80 max-w-[calc(100vw-2rem)]">
          <div className="bg-black/90 backdrop-blur-lg border border-white/20 rounded-xl shadow-2xl overflow-hidden animate-in slide-in-from-top-2 duration-300">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-white/20 bg-green-500/10">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <ShoppingBag className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-medium text-sm">Added to Cart!</h3>
                  <p className="text-green-400 text-xs">Item successfully added</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0 text-white/70 hover:text-white hover:bg-white/10"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-4">
              <div className="flex space-x-3">
                {/* Product Image */}
                <div className="w-16 h-16 bg-white/10 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Product Details */}
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium text-sm truncate">{item.name}</h4>
                  <p className="text-white/60 text-xs capitalize">{item.category}</p>
                  <p className="text-white font-medium text-sm mt-1">{item.price}</p>
                  
                  {/* Options */}
                  <div className="flex space-x-3 mt-1">
                    {item.selectedColor && (
                      <span className="text-xs text-white/50">Color: {item.selectedColor}</span>
                    )}
                    {item.selectedSize && (
                      <span className="text-xs text-white/50">Size: {item.selectedSize}</span>
                    )}
                  </div>
                  
                  {/* Quantity */}
                  <div className="mt-1">
                    <span className="text-xs text-white/50">Qty: {item.quantity}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 mt-4">
                <Link 
                  to={`/gifts/for-her/${item.category}/${item.id}`}
                  className="flex-1"
                  onClick={onClose}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 text-xs"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    View Item
                  </Button>
                </Link>
                <Button
                  size="sm"
                  className="flex-1 bg-white text-black hover:bg-white/90 text-xs"
                  onClick={() => {
                    onViewCart();
                    onClose();
                  }}
                >
                  <ShoppingCart className="h-3 w-3 mr-1" />
                  View Cart
                </Button>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="h-1 bg-white/10">
              <div 
                className="h-full bg-green-500 animate-pulse"
                style={{
                  animation: 'shrink 4s linear forwards'
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </>
  );
};

export default CartPopup;
