import React, { useEffect } from 'react';
import { X, Heart, Eye, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface WishlistPopupProps {
  isVisible: boolean;
  item: {
    id: string;
    name: string;
    price: string;
    image: string;
    category: string;
  } | null;
  onClose: () => void;
  onViewWishlist: () => void;
  onAddToCart: () => void;
}

const WishlistPopup: React.FC<WishlistPopupProps> = ({ 
  isVisible, 
  item, 
  onClose, 
  onViewWishlist, 
  onAddToCart 
}) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 4000); // Auto close after 4 seconds

      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible || !item) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />

      {/* Popup positioned at center */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-black/90 backdrop-blur-lg border border-white/20 rounded-xl shadow-2xl overflow-hidden animate-in slide-in-from-top-2 duration-300">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/20">
            <div className="flex items-center space-x-3">
              <Heart className="h-6 w-6 text-white fill-current" />
              <h2 className="text-xl font-serif text-white">
                Added to Wishlist!
              </h2>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Product Details */}
            <div className="flex space-x-4">
              {/* Product Image */}
              <div className="w-20 h-20 bg-white/10 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Product Info */}
              <div className="flex-1 min-w-0">
                <h3 className="text-white font-medium text-lg truncate">{item.name}</h3>
                <p className="text-white/60 text-sm capitalize mb-2">{item.category}</p>
                <p className="text-white font-medium text-lg">{item.price}</p>
              </div>
            </div>

            {/* Success Message */}
            <div className="text-center py-4">
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <Heart className="h-6 w-6 text-white fill-current" />
              </div>
              <p className="text-white text-lg font-medium">Added to your wishlist!</p>
              <p className="text-white/60 text-sm mt-1">💝 Saved for easy access later</p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                className="w-full bg-white text-black hover:bg-white/90 font-medium"
                onClick={() => {
                  onAddToCart();
                  onClose();
                }}
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </Button>

              <Button
                variant="outline"
                className="w-full border-red-400/30 text-red-400 hover:bg-red-500/10"
                onClick={() => {
                  onViewWishlist();
                  onClose();
                }}
              >
                <Heart className="h-4 w-4 mr-2 fill-current" />
                View Wishlist
              </Button>

              <Link
                to={`/gifts/for-her/${item.category}/${item.id}`}
                className="block"
                onClick={onClose}
              >
                <Button
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Item Details
                </Button>
              </Link>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="h-1 bg-white/10">
            <div
              className="h-full bg-red-500"
              style={{
                animation: 'shrink 4s linear forwards'
              }}
            />
          </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </>
  );
};

export default WishlistPopup;
