import React from 'react';
import { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCartWishlist } from '@/contexts/CartWishlistContext';

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
}

const Cart: React.FC<CartProps> = ({ isOpen, onClose }) => {
  const { 
    state, 
    removeFromCart, 
    updateCartQuantity, 
    clearCart, 
    getCartTotal, 
    getCartItemCount 
  } = useCartWishlist();

  const formatPrice = (price: string) => {
    const numericPrice = parseFloat(price.replace(/[^0-9.]/g, ''));
    return `$${numericPrice.toFixed(2)}`;
  };

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(id);
    } else {
      updateCartQuantity(id, newQuantity);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      
      {/* Cart Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full max-w-md bg-black/90 backdrop-blur-lg border-l border-white/20 z-50 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/20">
            <div className="flex items-center space-x-3">
              <ShoppingBag className="h-6 w-6 text-white" />
              <h2 className="text-xl font-serif text-white">
                Shopping Cart ({getCartItemCount()})
              </h2>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {state.cart.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <ShoppingBag className="h-16 w-16 text-white/30 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Your cart is empty</h3>
                <p className="text-white/60 text-sm">Add some beautiful items to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {state.cart.map((item) => (
                  <div key={`${item.id}-${item.selectedColor}-${item.selectedSize}`} className="bg-white/5 rounded-lg p-4 border border-white/10">
                    <div className="flex space-x-4">
                      {/* Product Image */}
                      <div className="w-16 h-16 bg-white/10 rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      
                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-white font-medium text-sm truncate">{item.name}</h4>
                        <p className="text-white/60 text-xs capitalize">{item.category}</p>
                        
                        {/* Color and Size */}
                        <div className="flex space-x-2 mt-1">
                          {item.selectedColor && (
                            <span className="text-xs text-white/50">Color: {item.selectedColor}</span>
                          )}
                          {item.selectedSize && (
                            <span className="text-xs text-white/50">Size: {item.selectedSize}</span>
                          )}
                        </div>
                        
                        {/* Price and Quantity Controls */}
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-white font-medium text-sm">{formatPrice(item.price)}</span>
                          
                          <div className="flex items-center space-x-2">
                            {/* Quantity Controls */}
                            <div className="flex items-center space-x-1 bg-white/10 rounded-lg">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                className="h-8 w-8 p-0 text-white hover:bg-white/20"
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="text-white text-sm w-8 text-center">{item.quantity}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                className="h-8 w-8 p-0 text-white hover:bg-white/20"
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            
                            {/* Remove Button */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFromCart(item.id)}
                              className="h-8 w-8 p-0 text-red-400 hover:bg-red-500/20"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {state.cart.length > 0 && (
            <div className="border-t border-white/20 p-6 space-y-4">
              {/* Total */}
              <div className="flex items-center justify-between">
                <span className="text-lg font-medium text-white">Total:</span>
                <span className="text-xl font-bold text-white">${getCartTotal().toFixed(2)}</span>
              </div>
              
              {/* Action Buttons */}
              <div className="space-y-2">
                <Button 
                  className="w-full bg-white text-black hover:bg-white/90 font-medium"
                  size="lg"
                >
                  Checkout
                </Button>
                <Button 
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                  onClick={clearCart}
                >
                  Clear Cart
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Cart;
