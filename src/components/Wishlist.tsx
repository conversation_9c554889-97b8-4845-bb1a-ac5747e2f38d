import React from 'react';
import { X, Heart, ShoppingCart, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCartWishlist } from '@/contexts/CartWishlistContext';
import { Link } from 'react-router-dom';

interface WishlistProps {
  isOpen: boolean;
  onClose: () => void;
}

const Wishlist: React.FC<WishlistProps> = ({ isOpen, onClose }) => {
  const { 
    state, 
    removeFromWishlist, 
    clearWishlist, 
    addToCart,
    getWishlistItemCount 
  } = useCartWishlist();

  const handleAddToCart = (item: any) => {
    addToCart({
      id: item.id,
      name: item.name,
      price: item.price,
      image: item.image,
      category: item.category,
      quantity: 1
    });
  };

  const handleMoveToCart = (item: any) => {
    handleAddToCart(item);
    removeFromWishlist(item.id);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      
      {/* Wishlist Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full max-w-md bg-black/90 backdrop-blur-lg border-l border-white/20 z-50 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/20">
            <div className="flex items-center space-x-3">
              <Heart className="h-6 w-6 text-red-400 fill-current" />
              <h2 className="text-xl font-serif text-white">
                Wishlist ({getWishlistItemCount()})
              </h2>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Wishlist Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {state.wishlist.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <Heart className="h-16 w-16 text-white/30 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">Your wishlist is empty</h3>
                <p className="text-white/60 text-sm">Save items you love for later</p>
              </div>
            ) : (
              <div className="space-y-4">
                {state.wishlist.map((item) => (
                  <div key={item.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                    <div className="flex space-x-4">
                      {/* Product Image */}
                      <Link 
                        to={`/gifts/for-her/${item.category}/${item.id}`}
                        onClick={onClose}
                        className="w-20 h-20 bg-white/10 rounded-lg overflow-hidden flex-shrink-0 hover:opacity-80 transition-opacity"
                      >
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      </Link>
                      
                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <Link 
                          to={`/gifts/for-her/${item.category}/${item.id}`}
                          onClick={onClose}
                          className="block hover:opacity-80 transition-opacity"
                        >
                          <h4 className="text-white font-medium text-sm truncate">{item.name}</h4>
                          <p className="text-white/60 text-xs capitalize">{item.category}</p>
                          <p className="text-white font-medium text-sm mt-1">{item.price}</p>
                        </Link>
                        
                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2 mt-3">
                          <Button
                            size="sm"
                            onClick={() => handleMoveToCart(item)}
                            className="bg-white/10 hover:bg-white/20 text-white border-white/30 text-xs px-3 py-1 h-7"
                          >
                            <ShoppingCart className="h-3 w-3 mr-1" />
                            Add to Cart
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFromWishlist(item.id)}
                            className="h-7 w-7 p-0 text-red-400 hover:bg-red-500/20"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {state.wishlist.length > 0 && (
            <div className="border-t border-white/20 p-6 space-y-4">
              {/* Action Buttons */}
              <div className="space-y-2">
                <Button 
                  className="w-full bg-white text-black hover:bg-white/90 font-medium"
                  size="lg"
                  onClick={() => {
                    state.wishlist.forEach(item => handleAddToCart(item));
                    clearWishlist();
                  }}
                >
                  Add All to Cart
                </Button>
                <Button 
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                  onClick={clearWishlist}
                >
                  Clear Wishlist
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Wishlist;
