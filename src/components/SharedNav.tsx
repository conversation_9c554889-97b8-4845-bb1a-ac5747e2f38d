import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>u, Heart, User, ShoppingBag, ArrowLeft } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import CartSidebar from "./CartSidebar";
import WishlistSidebar from "./WishlistSidebar";

const SharedNav = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentMenu, setCurrentMenu] = useState("main");
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isWishlistOpen, setIsWishlistOpen] = useState(false);

  return (
    <nav className="sticky top-0 z-50 px-8 py-6 bg-white/10 backdrop-blur-sm">
      <div className="grid grid-cols-3 items-center">
        <div className="flex justify-start">
          <Sheet open={isMenuOpen} onOpenChange={(open) => {
            setIsMenuOpen(open);
            if (open) {
              setCurrentMenu("main"); // Reset to main menu when opening
            }
          }}>
            <SheetTrigger asChild>
              <button
                className="flex items-center space-x-2 text-white hover:text-gray-200 transition-colors"
              >
                <Menu size={20} />
                <span className="text-sm font-light tracking-wide">MENU</span>
              </button>
            </SheetTrigger>
            <SheetContent side="left" className="bg-white/10 backdrop-blur-sm border-none w-full max-w-none text-white p-0 sm:max-w-sm">
              <div className="h-full flex flex-col p-8 pt-20">
                {currentMenu === "main" && (
                  <>
                    <div className="flex-grow flex flex-col space-y-6">
                      <div
                        className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer"
                        onClick={() => setCurrentMenu("gifts")}
                      >
                        <span className="text-lg font-light">Gifts</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">New</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Bags and Wallets</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Women</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Men</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Jewelry</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Watches</span>
                        <span>›</span>
                      </div>
                      <div className="flex items-center justify-between text-white hover:text-white/80 transition-colors cursor-pointer">
                        <span className="text-lg font-light">Perfumes</span>
                        <span>›</span>
                      </div>
                    </div>
                    <div className="flex-shrink-0 mt-8">
                      <div className="space-y-3 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                        <div className="mt-4 space-y-2">
                          <div className="flex items-center gap-2 text-white/80 hover:text-white transition-colors cursor-pointer">
                            <span className="uppercase tracking-wider">ADDRESS</span>
                            <span>→</span>
                          </div>
                          <div className="flex items-center gap-2 text-white/80 hover:text-white transition-colors cursor-pointer">
                            <span className="uppercase tracking-wider">OPENING HOURS</span>
                            <span>→</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {currentMenu === "gifts" && (
                  <>
                    <div className="flex items-center gap-3 mb-8">
                      <button
                        onClick={() => setCurrentMenu("main")}
                        className="text-white/80 hover:text-white transition-colors"
                      >
                        <ArrowLeft size={20} />
                      </button>
                      <span className="text-white/80 font-light">BACK</span>
                    </div>

                    <div className="mb-8">
                      <h2 className="text-2xl font-light text-white">Gifts</h2>
                    </div>

                    <div className="flex-grow flex flex-col space-y-6">
                      <Link
                        to="/gifts/for-her"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Her
                      </Link>
                      <Link
                        to="/gifts/for-home"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Home
                      </Link>
                      <Link
                        to="/gifts/for-babies"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Gifts for Babies
                      </Link>
                      <Link
                        to="/gifts/personalization"
                        className="text-white hover:text-white/80 transition-colors text-lg font-light"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Personalization
                      </Link>
                    </div>

                    <div className="flex-shrink-0 mt-8">
                      <Separator className="mb-4 bg-white/20" />
                      <div className="space-y-3 text-sm font-light">
                        <p className="text-white/80">Can we help you?</p>
                        <p className="text-white tracking-wider">+1.866.LORIYA</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
        <div className="text-center font-serif text-2xl font-light tracking-wider text-white">
          <Link to="/" className="hover:text-gray-200 transition-colors">
            LORIYA
          </Link>
        </div>
        <div className="flex justify-end items-center space-x-4 md:space-x-6 text-white">
          <button className="text-sm font-light tracking-wide hover:text-gray-200 transition-colors hidden md:block">
            Contact Us
          </button>
          <button
            className="hover:text-gray-200 transition-colors"
            onClick={() => setIsWishlistOpen(true)}
          >
            <Heart size={20} />
          </button>
          <button className="hover:text-gray-200 transition-colors">
            <User size={20} />
          </button>
          <button
            className="relative hover:text-gray-200 transition-colors"
            onClick={() => setIsCartOpen(true)}
          >
            <ShoppingBag size={20} />
            <span className="absolute -top-2 -right-2 bg-white text-black text-[10px] rounded-full h-4 w-4 flex items-center justify-center font-bold">
              0
            </span>
          </button>
        </div>
      </div>

      {/* Cart Sidebar */}
      <CartSidebar
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
      />

      {/* Wishlist Sidebar */}
      <WishlistSidebar
        isOpen={isWishlistOpen}
        onClose={() => setIsWishlistOpen(false)}
      />
    </nav>
  );
};

export default SharedNav;