import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CartSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  cartItems?: any[]; // You can define proper cart item type later
}

const CartSidebar: React.FC<CartSidebarProps> = ({ isOpen, onClose, cartItems = [] }) => {
  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed inset-y-0 right-0 z-50 w-full max-w-md bg-black/90 backdrop-blur-lg border-l border-white/20 shadow-2xl transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/20">
          <h2 className="text-xl font-serif text-white">
            Shopping Cart
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/10 rounded-full p-2"
          >
            <X className="h-5 w-5" />
            <span className="ml-2 text-sm">Close</span>
          </Button>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full">
          {/* Empty State */}
          {cartItems.length === 0 && (
            <div className="flex-1 flex flex-col items-center justify-center px-6 py-12">
              <div className="text-center space-y-6">
                <div className="text-white text-lg font-medium">
                  Your cart is empty.
                </div>
              </div>
            </div>
          )}

          {/* Cart Items - when you have items */}
          {cartItems.length > 0 && (
            <div className="flex-1 overflow-y-auto p-6">
              {/* Cart items will go here */}
              <div className="space-y-4">
                {cartItems.map((item, index) => (
                  <div key={index} className="flex space-x-4 p-4 bg-white/5 rounded-lg">
                    {/* Item content */}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t border-white/20 p-6">
            <div className="space-y-3 text-sm font-light">
              <p className="text-white/80">Can we help you?</p>
              <p className="text-white tracking-wider">+1.866.LORIYA</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CartSidebar;
